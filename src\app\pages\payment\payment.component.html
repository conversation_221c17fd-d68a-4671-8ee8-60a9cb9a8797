<!-- src/app/payment/payment.component.html -->
<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Finaliser votre réservation</h1>
      <p class="text-gray-600">Complétez votre paiement pour confirmer votre réservation</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Payment Form Section -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-md p-6">
          <!-- Card Payment Form -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Informations de carte</h3>
            <div class="space-y-4">
              <div>
                <label for="cardName" class="block text-sm font-medium text-gray-700 mb-1">
                  Nom sur la carte
                </label>
                <input
                  id="cardName"
                  type="text"
                  [(ngModel)]="cardForm.cardName"
                  class="card-input w-full border border-gray-300 rounded-md py-3 px-4 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                  placeholder="Jean Dupont"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Détails de la carte
                </label>
                <div id="card-element" class="w-full border border-gray-300 rounded-md py-3 px-4"></div>
              </div>
              <div *ngIf="paymentError" class="text-red-600 text-sm">{{ paymentError }}</div>
              <div *ngIf="paymentSuccess" class="text-green-600 text-sm">Paiement réussi !</div>
            </div>
          </div>

          <!-- Security Features -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Sécurité et protection</h3>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div *ngFor="let feature of securityFeatures" class="flex items-center">
                <span class="material-icons text-green-600 mr-2">{{ feature.icon }}</span>
                <span class="text-sm text-gray-700">{{ feature.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Summary Sidebar -->
      <div class="lg:col-span-1">
        <div class="order-summary bg-white rounded-lg shadow-md p-6 sticky top-8">
          <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Détails de la réservation</h2>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600">Événement:</span>
                <span class="font-medium text-gray-900 text-right">{{ reservationDetails.eventTitle }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Date:</span>
                <span class="font-medium text-gray-900">{{ reservationDetails.eventDate | date: 'dd MMMM yyyy' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Heure:</span>
                <span class="font-medium text-gray-900">{{ reservationDetails.eventTime }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Table:</span>
                <span class="font-medium text-gray-900">N° {{ reservationDetails.tableNumber }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Invités:</span>
                <span class="font-medium text-gray-900">{{ reservationDetails.numberOfGuests }} personnes</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Réservation:</span>
                <span class="font-medium text-gray-900">{{ reservationDetails.reservationId }}</span>
              </div>
            </div>
          </div>

          <div class="border-t border-gray-200 pt-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Récapitulatif</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600">
                  {{ reservationDetails.numberOfGuests }} × {{ reservationDetails.pricePerPerson }} TND
                </span>
                <span class="font-medium text-gray-900">{{ orderSummary.subtotal }} TND</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Frais de service</span>
                <span class="font-medium text-gray-900">{{ orderSummary.serviceFee }} TND</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Taxes et frais</span>
                <span class="font-medium text-gray-900">{{ orderSummary.taxes }} TND</span>
              </div>
              <div class="border-t border-gray-200 pt-3">
                <div class="flex justify-between">
                  <span class="text-lg font-semibold text-gray-900">Total</span>
                  <span class="text-lg font-bold text-gold-600">{{ orderSummary.total }} TND</span>
                </div>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <button
              (click)="processPayment()"
              class="payment-button w-full bg-gold-600 hover:bg-gold-700 text-white py-3 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center">
              <span class="material-icons mr-2">lock</span>
              Payer {{ orderSummary.total }} TND
            </button>
            <p class="text-xs text-gray-500 text-center">
              En cliquant sur "Payer", vous acceptez nos
              <a href="#" class="text-gold-600 hover:text-gold-700">conditions d'utilisation</a>
              et notre
              <a href="#" class="text-gold-600 hover:text-gold-700">politique de confidentialité</a>.
            </p>
          </div>

          <div class="border-t border-gray-200 pt-6 mt-6">
            <div class="flex items-center justify-center space-x-4">
              <div class="flex items-center text-xs text-gray-500 security-badge">
                <span class="material-icons text-green-600 mr-1 text-sm">verified</span>
                <span>SSL Sécurisé</span>
              </div>
              <div class="flex items-center text-xs text-gray-500 security-badge">
                <span class="material-icons text-green-600 mr-1 text-sm">privacy_tip</span>
                <span>Données protégées</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-12 bg-white rounded-lg shadow-md p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <span class="material-icons text-gold-600 mr-2">event_available</span>
            Politique d'annulation
          </h3>
          <p class="text-gray-600 text-sm">
            Annulation gratuite jusqu'à 24h avant l'événement.
            Après ce délai, des frais d'annulation peuvent s'appliquer.
          </p>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <span class="material-icons text-gold-600 mr-2">support_agent</span>
            Support client
          </h3>
          <p class="text-gray-600 text-sm">
            Notre équipe est disponible 24h/7j pour vous aider.
            Contactez-nous au +216 XX XXX XXX ou par email.
          </p>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <span class="material-icons text-gold-600 mr-2">thumb_up</span>
            Garantie satisfaction
          </h3>
          <p class="text-gray-600 text-sm">
            Votre satisfaction est notre priorité.
            Si vous n'êtes pas satisfait, nous nous engageons à trouver une solution.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>