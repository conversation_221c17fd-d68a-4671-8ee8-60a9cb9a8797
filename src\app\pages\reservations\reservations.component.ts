import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReservationService } from '../../services/reservation.service';
import { EventService } from '../../services/event.service';
import { AuthService } from '../../services/auth.service';
import { Reservation } from '../../models/reservation.model';
import { Event } from '../../models/event.model';

@Component({
  selector: 'app-reservations',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './reservations.component.html',
  styleUrl: './reservations.component.scss'
})
export class ReservationsComponent implements OnInit {
  reservations: Reservation[] = [];
  events: Map<string, Event> = new Map();
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  selectedReservation: Reservation | null = null;
  showCancelConfirmation = false;

  constructor(
    private reservationService: ReservationService,
    private eventService: EventService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadReservations();
  }

  private loadReservations(): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.errorMessage = 'Vous devez être connecté pour voir vos réservations.';
      this.isLoading = false;
      return;
    }

    this.reservationService.getUserReservations(currentUser.id).subscribe({
      next: (reservations) => {
        this.reservations = reservations;
        this.loadEventDetails();
      },
      error: (error) => {
        console.error('Error loading reservations:', error);
        this.errorMessage = 'Une erreur est survenue lors du chargement des réservations.';
        this.isLoading = false;
      }
    });
  }

  private loadEventDetails(): void {
    const eventIds = [...new Set(this.reservations.map(r => r.eventId))];

    if (eventIds.length === 0) {
      this.isLoading = false;
      return;
    }

    let loadedCount = 0;

    eventIds.forEach(eventId => {
      this.eventService.getEventById(eventId).subscribe({
        next: (event) => {
          if (event) {
            this.events.set(eventId, event);
          }

          loadedCount++;
          if (loadedCount === eventIds.length) {
            this.isLoading = false;
          }
        },
        error: (error) => {
          console.error(`Error loading event ${eventId}:`, error);
          loadedCount++;
          if (loadedCount === eventIds.length) {
            this.isLoading = false;
          }
        }
      });
    });
  }

  getEventForReservation(reservation: Reservation): Event | undefined {
    return this.events.get(reservation.eventId);
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', year: 'numeric' });
  }

  getStatusLabel(status: 'pending' | 'confirmed' | 'cancelled'): string {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'confirmed':
        return 'Confirmée';
      case 'cancelled':
        return 'Annulée';
      default:
        return status;
    }
  }

  getStatusClass(status: 'pending' | 'confirmed' | 'cancelled'): string {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  showReservationDetails(reservation: Reservation): void {
    this.selectedReservation = reservation;
  }

  closeReservationDetails(): void {
    this.selectedReservation = null;
  }

  confirmCancelReservation(reservation: Reservation): void {
    this.selectedReservation = reservation;
    this.showCancelConfirmation = true;
  }

  cancelReservation(): void {
    if (!this.selectedReservation) {
      return;
    }

    this.reservationService.cancelReservation(this.selectedReservation.id).subscribe({
      next: (success) => {
        if (success) {
          this.successMessage = 'Réservation annulée avec succès.';
          this.showCancelConfirmation = false;

          // Update the reservation status in the local array
          const index = this.reservations.findIndex(r => r.id === this.selectedReservation?.id);
          if (index !== -1) {
            this.reservations[index] = {
              ...this.reservations[index],
              statutR: 'cancelled'
            };
          }

          // Close the details modal after a short delay
          setTimeout(() => {
            this.selectedReservation = null;
          }, 2000);
        } else {
          this.errorMessage = 'Impossible d\'annuler la réservation.';
        }
      },
      error: (error) => {
        console.error('Error cancelling reservation:', error);
        this.errorMessage = 'Une erreur est survenue lors de l\'annulation de la réservation.';
      }
    });
  }

  closeCancelConfirmation(): void {
    this.showCancelConfirmation = false;
  }

  canCancelReservation(reservation: Reservation): boolean {
    return reservation.statutR !== 'cancelled';
  }
}
